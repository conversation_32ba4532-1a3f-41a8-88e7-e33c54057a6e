import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:stable_money_flutter/app.dart';
import 'package:stable_money_flutter/app_initialization.dart';
import 'package:stable_money_flutter/components/base/dismiss_keyboard_wrapper.dart';
import 'package:stable_money_flutter/components/dynamic_bank_details/dynamic_bank_button/cubit/dynamic_bank_button_cubit.dart';
import 'package:stable_money_flutter/components/dynamic_widgets/navigation/dynamic_navigation_bar_cubit.dart';
import 'package:stable_money_flutter/components/explore_v2/video_minimal_widget.dart/global_mute_cubit.dart';
import 'package:stable_money_flutter/components/inherited/feature_flag_provider.dart';
import 'package:stable_money_flutter/env/flutter_flavor_config.dart';
import 'package:stable_money_flutter/gen/colors.gen.dart';
import 'package:stable_money_flutter/offline-video-downloading/video_download_cubit.dart';
import 'package:stable_money_flutter/pages/dynamic_bank_integration/repository/dynamic_bank_integration_repository.dart';
import 'package:stable_money_flutter/pages/dynamic_bank_integration/status/cubit/dynamic_bank_status_cubit.dart';
import 'package:stable_money_flutter/pages/fd_bank_revamped/cubit/fd_bank_v2_repository.dart';
import 'package:stable_money_flutter/pages/global/splash_authentication_cubit.dart';
import 'package:stable_money_flutter/pages/logout/logout_cubit.dart';
import 'package:stable_money_flutter/pages/logout/logout_repository.dart';
import 'package:stable_money_flutter/pages/pending_journey/vkyc/connect_button/cubit/vkyc_connect_button_cubit.dart';
import 'package:stable_money_flutter/pages/suryoday_credit_card/decrease_limit/cubit/suryoday_credit_card_decrease_limit_cubit.dart';
import 'package:stable_money_flutter/pages/suryoday_credit_card/increase_limit/cubit/suryoday_credit_card_increase_limit_cubit.dart';
import 'package:stable_money_flutter/pages/suryoday_credit_card/repository/suryoday_credit_card_repository.dart';
import 'package:stable_money_flutter/pages/suryoday_credit_card/status/cubit/suryoday_credit_card_status_cubit.dart';
import 'package:stable_money_flutter/providers/shared_preference_provider.dart';
import 'package:stable_money_flutter/utils/localization/custom_asset_loader.dart';
import 'package:stable_money_flutter/utils/theme_config.dart';
import 'pages/suryoday_credit_card/dashboard.dart/cubit/suryoday_credit_card_dashboard_cubit.dart';
import 'package:stable_money_flutter/firebase_options_stage.dart';

import 'pages/suryoday_credit_card/request_address_change/cubit/scc_address_change_cubit.dart';

void main() async {
  FlavorConfig(
    flavor: Flavor.staging,
    enableAnalytics: true,
    digioEnv: DigioEnv.prod,
    moEngageEnv: MoEngageEnv.sandbox,
    enableScreenRecording: false,
    appsFlyerDevAppKey: 'c9XrxNYTCX5u5VZJBTiNNH',
    moEngageAppId: 'J3JDWTPJ6K0377YL191ECNJQ',
    userExperiorAppId: '17fd1e16-4e06-4d8f-aae7-2a70c9d62c59',
    values: FlavorValues(
      baseUrl: 'https://staging-api.stablemoney.in/',
      toolsBaseUrl: 'https://staging-tools-api.stablemoney.in/',
      finserveBaseUrl: 'https://finserv-staging-api.stablemoney.in/',
      zohoBaseUrl: 'https://desk.zoho.in/',
      businessBaseUrl: 'https://business-staging-api.stablemoney.in/',
      connectionTimeoutMillis: const Duration(seconds: 30),
      receiveTimeoutMillis: const Duration(seconds: 30),
      sendTimeoutMillis: const Duration(seconds: 30),
      fincareBaseUrl: 'https://101.fincarebank.com/101FD/',
      unityBaseUrl: 'https://finserv-staging-api.stablemoney.in/unity/',
      ujjivanBaseUrl: 'https://online.ujjivansfb.in/fixed-deposit/',
      brokingIdentityBaseUrl: 'https://broking-staging-api.stablebonds.in/',
      indusindBaseUrl: 'https://finserv-staging-api.stablemoney.in/indusind/',
      suryodayCreditCardBaseUrl:
          'https://finserv-staging-api.stablemoney.in/scc/',
      webAppDomain: 'https://staging-app.stablemoney.in/',
      personalization: 'https://personalization-staging-api.stablemoney.in/',
      websiteBaseUrl: 'https://staging.stablemoney.in',
      dynamicBankFlowBaseUrl:
          'https://finserv-staging-api.stablemoney.in/fdservice/',
    ),
    androidAppId: 'in.stablemoney.app.stg',
    iOSAppId: '**********',
    pgCallback: 'https://staging-app.stablemoney.in/callback/payment',
    mixPanelToken: '4c70c3e53db4c5c1755727a7d983eeaf',
    dataDogClientId: 'pub2cffb80f39bf639794f8b945423dd4f9',
    dataDogApplicationId: '2983b9fc-b111-4699-bba0-e1169db9c3dc',
    appsFlyerTemplateId: 'HyJo',
    cfEnvironment: CFEnvironment.SANDBOX,
  );
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await ScreenUtil.ensureScreenSize();

  await initializeApp(
    firebaseOptions: DefaultFirebaseOptions.currentPlatform,
  );

  SharedPrefenceImpl sharedPrefenceImpl =
      await initializeLocalDBAndSocialLogins();
  await initializeAnalytics();

  SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);

  await SentryFlutter.init(
    (options) {
      options = FlavorConfig.getSentryOptions(options);
    },
    appRunner: () => runApp(
      SentryWidget(
        child: ColoredBox(
          color: ColorName.bgGrayColor,
          child: FeatureFlagsManager(
            child: EasyLocalization(
              supportedLocales: const [
                Locale('en', 'US'),
              ],
              useOnlyLangCode: true,
              path: 'assets/translations',
              assetLoader: CustomAssetLoader(
                remoteUrl: 'https://assets.stablemoney.in/translations',
              ),
              child: ProviderScope(
                overrides: [
                  sharedPrefenceImplProvider
                      .overrideWithValue(sharedPrefenceImpl),
                ],
                child: MultiBlocProvider(
                  providers: [
                    BlocProvider(
                      create: (context) => LogoutCubit(
                        LogoutRepository(),
                      ),
                    ),
                    BlocProvider(
                      create: (context) => VideoDownloadCubit(),
                    ),
                    BlocProvider(
                      create: (context) => VKYCConnectButtonCubit(),
                    ),
                    BlocProvider(
                      create: (context) => SccAddressChangeCubit(
                        CreditCardRepository(),
                      ),
                    ),
                    BlocProvider(
                      create: (context) => SplashAuthenticationCubit(),
                    ),
                    BlocProvider(
                      create: (context) => DynamicNavigationBarCubit(),
                    ),
                    BlocProvider(
                      create: (context) => SuryodayCreditCardDashboardCubit(
                        creditCardRepository: CreditCardRepository(),
                      ),
                    ),
                    BlocProvider(
                      create: (context) => GlobalMuteCubit(),
                    ),
                    BlocProvider(
                      create: (context) => CreditCardStatusCubit(
                        creditCardRepository: CreditCardRepository(),
                      ),
                    ),
                    BlocProvider(
                      create: (context) => CreditCardStatusCubit(
                        creditCardRepository: CreditCardRepository(),
                      ),
                    ),
                    BlocProvider(
                      create: (context) => DynamicBankButtonCubit(
                        fdBankV2Repository: FDBankV2Repository(),
                      ),
                    ),
                    BlocProvider(
                      create: (context) => SuryodayCreditCardIncreaseLimitCubit(
                        creditCardRepository: CreditCardRepository(),
                      ),
                    ),
                    BlocProvider(
                      create: (context) => SuryodayCreditCardDecreaseLimitCubit(
                        creditCardRepository: CreditCardRepository(),
                      ),
                    ),
                    BlocProvider(
                      create: (context) => DynamicBankStatusCubit(
                        dynamicBankIntegrationRepository:
                            DynamicBankIntegrationRepository(),
                      ),
                    ),
                  ],
                  child: DismissKeyboardWrapper(
                    child: Builder(
                      builder: (context) {
                        ScreenUtil.init(
                          context,
                          minTextAdapt: true,
                          splitScreenMode: true,
                          designSize: const Size(360, 800),
                        );
                        return App();
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  );
}
