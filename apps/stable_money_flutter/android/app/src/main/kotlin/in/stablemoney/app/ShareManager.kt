package `in`.stablemoney.app

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.util.Base64
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

class ShareManager(private val context: Context) {

    fun shareText(text: String, chooserTitle: String) {
        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, text)
        }
        val chooser = Intent.createChooser(intent, chooserTitle)
        chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(chooser)
    }

    fun shareImage(imagePath: String, chooserTitle: String, text: String?) {
        val file = File(imagePath)
        if (!file.exists()) return

        val newFile = File(context.cacheDir, "shared_image.jpg")
        if (!newFile.exists() || newFile.length() != file.length()) {
            file.copyTo(newFile, overwrite = true)
        }

        val uri: Uri =
            FileProvider.getUriForFile(context, "${context.packageName}.provider", newFile)

        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "image/*"
            putExtra(Intent.EXTRA_STREAM, uri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }

        if (!text.isNullOrEmpty()) {
            intent.putExtra(Intent.EXTRA_TEXT, text)
            intent.setType("image/*")
        }


        val chooser = Intent.createChooser(intent, chooserTitle)
        chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(chooser)
    }
}

class EmailManager(private val context: Context) {

    fun sendEmail(to: String, subject: String, body: String, attachmentBlobUrl: String? = null) {
        if (attachmentBlobUrl != null) {
            sendEmailWithAttachment(to, subject, body, attachmentBlobUrl)
        } else {
            sendSimpleEmail(to, subject, body)
        }
    }

    private fun sendSimpleEmail(to: String, subject: String, body: String) {
        val intent = Intent(Intent.ACTION_SENDTO).apply {
            data = Uri.parse("mailto:")
            putExtra(Intent.EXTRA_EMAIL, arrayOf(to))
            putExtra(Intent.EXTRA_SUBJECT, subject)
            putExtra(Intent.EXTRA_TEXT, body)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }

        try {
            context.startActivity(intent)
        } catch (e: Exception) {
            // If no email app is available, try with ACTION_SEND
            val fallbackIntent = Intent(Intent.ACTION_SEND).apply {
                type = "message/rfc822"
                putExtra(Intent.EXTRA_EMAIL, arrayOf(to))
                putExtra(Intent.EXTRA_SUBJECT, subject)
                putExtra(Intent.EXTRA_TEXT, body)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            val chooser = Intent.createChooser(fallbackIntent, "Send email via")
            chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(chooser)
        }
    }

    private fun sendEmailWithAttachment(to: String, subject: String, body: String, blobUrl: String) {
        try {
            // Download the blob URL content to a temporary file
            val attachmentFile = downloadBlobToFile(blobUrl)
            if (attachmentFile != null) {
                val uri = FileProvider.getUriForFile(context, "${context.packageName}.provider", attachmentFile)

                val intent = Intent(Intent.ACTION_SEND).apply {
                    type = "message/rfc822"
                    putExtra(Intent.EXTRA_EMAIL, arrayOf(to))
                    putExtra(Intent.EXTRA_SUBJECT, subject)
                    putExtra(Intent.EXTRA_TEXT, body)
                    putExtra(Intent.EXTRA_STREAM, uri)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }

                val chooser = Intent.createChooser(intent, "Send email via")
                chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(chooser)
            } else {
                // Fallback to simple email if attachment download fails
                sendSimpleEmail(to, subject, body)
            }
        } catch (e: Exception) {
            // Fallback to simple email if anything goes wrong
            sendSimpleEmail(to, subject, body)
        }
    }

    private fun downloadBlobToFile(blobUrl: String): File? {
        return try {
            val timestamp = System.currentTimeMillis()
            val tempFile = File(context.cacheDir, "email_attachment_$timestamp")

            val latch = CountDownLatch(1)
            var result: File? = null

            Handler(Looper.getMainLooper()).post {
                val webView = WebView(context)
                webView.settings.javaScriptEnabled = true

                val blobDownloader = BlobDownloader(tempFile) { file ->
                    result = file
                    latch.countDown()
                }

                webView.addJavascriptInterface(blobDownloader, "BlobDownloader")
                webView.webViewClient = object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        // Inject JavaScript to convert blob URL to base64
                        val script = """
                            (function() {
                                fetch('$blobUrl')
                                    .then(response => response.blob())
                                    .then(blob => {
                                        const reader = new FileReader();
                                        reader.onload = function() {
                                            const base64 = reader.result.split(',')[1];
                                            BlobDownloader.onBlobData(base64);
                                        };
                                        reader.onerror = function() {
                                            BlobDownloader.onError('Failed to read blob');
                                        };
                                        reader.readAsDataURL(blob);
                                    })
                                    .catch(error => {
                                        BlobDownloader.onError('Failed to fetch blob: ' + error.message);
                                    });
                            })();
                        """.trimIndent()

                        webView.evaluateJavascript(script, null)
                    }
                }

                // Load a minimal HTML page to execute JavaScript
                webView.loadData("<html><body></body></html>", "text/html", "UTF-8")
            }

            // Wait for the download to complete (with timeout)
            if (latch.await(30, TimeUnit.SECONDS)) {
                result
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    private class BlobDownloader(
        private val outputFile: File,
        private val callback: (File?) -> Unit
    ) {
        @JavascriptInterface
        fun onBlobData(base64Data: String) {
            try {
                val decodedBytes = Base64.decode(base64Data, Base64.DEFAULT)
                FileOutputStream(outputFile).use { fos ->
                    fos.write(decodedBytes)
                }
                callback(outputFile)
            } catch (e: Exception) {
                callback(null)
            }
        }

        @JavascriptInterface
        fun onError(error: String) {
            callback(null)
        }
    }
}
