# Email Bridge Component

The EmailComponent is a bridge component that allows triggering email functionality from the web interface with support for recipient, subject, body, and blob URL attachments.

## Usage

The EmailComponent is registered as "email" in the bridge components and supports the following event:

### sendEmail Event

Triggers the device's default email client with pre-filled recipient, subject, body, and optional attachment from a blob URL.

#### Request Format

```javascript
// Simple email without attachment
bridge.send("sendEmail", {
  to: "<EMAIL>",
  subject: "Email Subject",
  body: "Email body content"
})

// Email with blob URL attachment
bridge.send("sendEmail", {
  to: "<EMAIL>",
  subject: "Email Subject",
  body: "Email body content",
  attachmentBlobUrl: "blob:https://example.com/12345-abcde"
})
```

#### Parameters

- `to` (String, required): The recipient email address
- `subject` (String, required): The email subject line
- `body` (String, required): The email body content
- `attachmentBlobUrl` (String, optional): A blob URL created in JavaScript to attach as a file

#### Response

On success, the component replies with the original message.

On error, the component replies with:
```json
{
  "status": "error",
  "message": "Error description"
}
```

## Implementation Details

The EmailComponent uses Android's Intent system to launch the email client:

### Without Attachments
1. **Primary method**: Uses `Intent.ACTION_SENDTO` with `mailto:` scheme
2. **Fallback method**: Uses `Intent.ACTION_SEND` with `message/rfc822` MIME type if no email app handles the primary intent

### With Blob URL Attachments
1. **Blob Processing**: Uses a WebView with JavaScript injection to convert blob URLs to base64 data
2. **File Creation**: Downloads and saves the blob content to a temporary file in the app's cache directory
3. **File Sharing**: Uses `FileProvider` to create a secure URI for the attachment
4. **Email Intent**: Uses `Intent.ACTION_SEND` with `message/rfc822` MIME type and `Intent.EXTRA_STREAM` for the attachment

### Blob URL Processing Steps
1. Creates a WebView with JavaScript enabled
2. Injects JavaScript to fetch the blob URL and convert it to base64
3. Transfers the base64 data to native code via `JavascriptInterface`
4. Decodes the base64 data and writes it to a temporary file
5. Creates a FileProvider URI for secure file sharing

This ensures compatibility with various email clients and secure handling of blob URL attachments.

## Error Handling

The component handles the following error scenarios:

- Invalid email data (missing required fields)
- Null context
- Exceptions during intent creation or launching
- Blob URL download failures (falls back to simple email without attachment)
- WebView JavaScript execution errors
- File creation or writing errors
- Timeout during blob processing (30-second limit)

All errors are logged and returned as error responses to the caller. When blob URL processing fails, the component gracefully falls back to sending a simple email without the attachment.
