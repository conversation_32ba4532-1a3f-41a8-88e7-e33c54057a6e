package `in`.stablemoney.app.bridge

import android.util.Log
import dev.hotwire.core.bridge.BridgeComponent
import dev.hotwire.core.bridge.BridgeDelegate
import dev.hotwire.core.bridge.Message
import dev.hotwire.navigation.destinations.HotwireDestination
import `in`.stablemoney.app.EmailManager
import kotlinx.serialization.Serializable

class EmailComponent(
    name: String,
    private val emailDelegate: BridgeDelegate<HotwireDestination>
) : BridgeComponent<HotwireDestination>(name, emailDelegate) {
    private val context = emailDelegate.destination.fragment.context
    private val emailManager: EmailManager
        get() {
            val context = emailDelegate.destination.fragment.context
                ?: throw IllegalStateException("Context is null")
            return EmailManager(context)
        }

    override fun onReceive(message: Message) {
        // Handle incoming messages based on the message `event`.
        when (message.event) {
            SEND_EMAIL -> handleSendEmail(message)
            else -> Log.w("EmailComponent", "Unknown event for message: $message")
        }
    }

    private fun handleSendEmail(message: Message) {
        val data = message.data<EmailData>() ?: run {
            replyTo(message.event, EmailError(message = "Invalid email data"))
            return
        }
        context ?: run {
            replyTo(message.event, EmailError(message = "Context is null"))
            return
        }

        try {
            emailManager.sendEmail(data.to, data.subject, data.body, data.attachmentBlobUrl)
            replyWith(message)
        } catch (e: Exception) {
            Log.e("EmailComponent", "Error sending email", e)
            replyTo(message.event, EmailError(message = e.message ?: "Unknown error"))
        }
    }

    @Serializable
    data class EmailData(
        val to: String,
        val subject: String,
        val body: String,
        val attachmentBlobUrl: String? = null
    )

    @Serializable
    data class EmailError(
        val status: String = "error",
        val message: String
    )

    companion object Events {
        const val SEND_EMAIL = "sendEmail"
    }
}
